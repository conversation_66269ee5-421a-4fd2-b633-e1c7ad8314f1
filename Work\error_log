[28-Jun-2025 14:41:58 UTC] Products::search - No log object available
[28-Jun-2025 14:41:58 UTC] Products::search - Simple search returned: 0 results
[28-Jun-2025 14:41:58 UTC] Products::search - Complex search returned: 0 results
[28-Jun-2025 14:43:48 UTC] Products::search - No log object available
[28-Jun-2025 14:43:48 UTC] Products::search - Simple search returned: 0 results
[28-Jun-2025 14:43:48 UTC] Products::search - Complex search returned: 0 results
[28-Jun-2025 14:43:52 UTC] Products::search - No log object available
[28-Jun-2025 14:43:52 UTC] Products::search - Simple search returned: 0 results
[28-Jun-2025 14:43:52 UTC] Products::search - Complex search returned: 0 results
[28-Jun-2025 14:43:55 UTC] Products::search - No log object available
[28-Jun-2025 14:43:55 UTC] Products::search - Simple search returned: 0 results
[28-Jun-2025 14:43:55 UTC] Products::search - Complex search returned: 0 results
[28-Jun-2025 14:48:12 UTC] Products::search - No log object available
[28-Jun-2025 14:48:12 UTC] Products::search - Simple search returned: 0 results
[28-Jun-2025 14:48:12 UTC] Products::search - Complex search returned: 0 results
[28-Jun-2025 14:48:29 UTC] Products::search - No log object available
[28-Jun-2025 14:48:29 UTC] Products::search - Simple search returned: 0 results
[28-Jun-2025 14:48:29 UTC] Products::search - Complex search returned: 0 results
[29-Jun-2025 04:20:53 UTC] PHP Fatal error:  Uncaught Exception: Error: Could not load model sale/request! in /home/<USER>/storage_theme25/theme/ModelProcessor.php:132
Stack trace:
#0 /home/<USER>/storage_theme25/theme/ModelProcessor.php(36): Theme25\ModelProcessor->loadModel('sale/request', false, NULL)
#1 /home/<USER>/theme25/system/engine/modelprocessor.php(26): Theme25\ModelProcessor->process('sale/request')
#2 /home/<USER>/storage_theme25/modification/system/engine/loader.php(55): ModelProcessor->process('sale/request')
#3 /home/<USER>/storage_theme25/theme/Controller.php(87): Loader->model('sale/request')
#4 /home/<USER>/storage_theme25/theme/Controller.php(106): Theme25\Controller->loadModelAs('sale/request', 'requests')
#5 /home/<USER>/storage_theme25/theme/Backend/Controller/Sale/Request.php(35): Theme25\Controller->loadModelsAs(Array)
#6 /home/<USER>/storage_theme25/theme/Backend/Controller/Sale/Request.php(18): Theme25\Backend\Controller\Sale\Request->prepareRequestListData()
#7 [internal function]: Theme25\Backend\Controller\Sale\Request->index()
#8 in /home/<USER>/storage_theme25/theme/ModelProcessor.php on line 132
[29-Jun-2025 07:11:50 UTC] PHP Fatal error:  Uncaught Exception: Error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'OR LOWER(pd.name) LIKE '%таня%' OR LOWER(p.model) LIKE '%таня%' OR LO...' at line 16<br />Error No: 1064<br />
            SELECT DISTINCT
                p.product_id,
                pd.name,
                p.model,
                p.sku,
                p.price,
                p.status,
                'partial' as match_type
            FROM
                oc_product p
            LEFT JOIN
                oc_product_description pd ON (p.product_id = pd.product_id)
            WHERE
                pd.language_id = '2'
                AND p.status = '1'
                AND (( OR LOWER(pd.name) LIKE '%таня%' OR LOWER(p.model) LIKE '%таня%' OR LOWER(p.sku) LIKE '%таня%' OR LOWER(pd.name) LIKE '%tanya%' OR LOWER(p.model) LIKE '%tanya%' OR LOWER(p.sku) LIKE '%tanya%') AND ( OR LOWER(pd.name) LIKE '%петрова%' OR LOWER(p in /home/<USER>/theme25/system/library/db/mysqli.php on line 40
[29-Jun-2025 07:12:10 UTC] PHP Fatal error:  Uncaught Exception: Error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'OR LOWER(pd.name) LIKE '%таня%' OR LOWER(p.model) LIKE '%таня%' OR LO...' at line 16<br />Error No: 1064<br />
            SELECT DISTINCT
                p.product_id,
                pd.name,
                p.model,
                p.sku,
                p.price,
                p.status,
                'partial' as match_type
            FROM
                oc_product p
            LEFT JOIN
                oc_product_description pd ON (p.product_id = pd.product_id)
            WHERE
                pd.language_id = '2'
                AND p.status = '1'
                AND (( OR LOWER(pd.name) LIKE '%таня%' OR LOWER(p.model) LIKE '%таня%' OR LOWER(p.sku) LIKE '%таня%' OR LOWER(pd.name) LIKE '%tanya%' OR LOWER(p.model) LIKE '%tanya%' OR LOWER(p.sku) LIKE '%tanya%') AND ( OR LOWER(pd.name) LIKE '%петрова%' OR LOWER(p in /home/<USER>/theme25/system/library/db/mysqli.php on line 40
