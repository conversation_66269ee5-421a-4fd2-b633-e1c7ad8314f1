<?php

namespace Theme25\Backend\Controller\Common\GlobalSearch;

class Orders extends \Theme25\ControllerSubMethods {


    /**
     * Транслитерационна таблица кирилица -> латиница
     */
    private $translitMap = [
        'а' => 'a', 'б' => 'b', 'в' => 'v', 'г' => 'g', 'д' => 'd', 'е' => 'e',
        'ж' => 'zh', 'з' => 'z', 'и' => 'i', 'й' => 'y', 'к' => 'k', 'л' => 'l',
        'м' => 'm', 'н' => 'n', 'о' => 'o', 'п' => 'p', 'р' => 'r', 'с' => 's',
        'т' => 't', 'у' => 'u', 'ф' => 'f', 'х' => 'h', 'ц' => 'c', 'ч' => 'ch',
        'ш' => 'sh', 'щ' => 'sht', 'ъ' => 'a', 'ь' => 'y', 'ю' => 'yu', 'я' => 'ya'
    ];

    public function __construct($registry) {
        parent::__construct($registry);
    }

    /**
     * Търсене в поръчки (за dropdown) с кеширане
     */
    public function search($query) {
        // Проверяваме кеша първо
        $cacheKey = $this->generateCacheKey('orders_dropdown', $query, 5);
        $cachedResults = $this->getCachedResults($cacheKey);

        $this->logDev("Orders::search - Starting search for query: '$query'");  

        if ($cachedResults !== null) {
            $this->logDev("Orders::search - Returning cached results: " . count($cachedResults) . " items");
            return $cachedResults;
        }

        // Ако няма кеширани резултати, извършваме търсенето
        $results = $this->performOptimizedSearch($query, 5);

        // Кешираме резултатите
        $this->cacheResults($cacheKey, $results);

        $this->logDev("Orders::search - Fresh search returned: " . count($results) . " results");
        return $results;
    }

    /**
     * Генерира ключ за кеша
     */
    private function generateCacheKey($type, $query, $limit = null, $offset = null) {
        $keyData = [
            'type' => $type,
            'query' => mb_strtolower(trim($query)),
            'limit' => $limit,
            'offset' => $offset
        ];
        return 'search_' . md5(serialize($keyData));
    }

    /**
     * Получава кеширани резултати
     */
    private function getCachedResults($cacheKey) {
        $cacheFile = $this->cacheDir . $cacheKey . '.cache';

        if (!file_exists($cacheFile)) {
            return null;
        }

        $cacheData = file_get_contents($cacheFile);
        if ($cacheData === false) {
            return null;
        }

        $data = unserialize($cacheData);
        if ($data === false || !isset($data['timestamp']) || !isset($data['results'])) {
            return null;
        }

        // Проверяваме дали кешът е изтекъл
        if (time() - $data['timestamp'] > $this->cacheExpiry) {
            unlink($cacheFile);
            return null;
        }

        return $data['results'];
    }

    /**
     * Кешира резултатите
     */
    private function cacheResults($cacheKey, $results) {

        $cacheFile = $this->cacheDir . $cacheKey . '.cache';
        $cacheData = [
            'timestamp' => time(),
            'results' => $results
        ];

        file_put_contents($cacheFile, serialize($cacheData));
    }

    /**
     * Изчиства кеша за поръчки
     */
    public function clearCache() {
        if (is_dir($this->cacheDir)) {
            $files = glob($this->cacheDir . 'search_*.cache');
            foreach ($files as $file) {
                if (strpos($file, 'orders') !== false) {
                    unlink($file);
                }
            }
        }
    }

    /**
     * Оптимизирано търсене с релевантност
     */
    private function performOptimizedSearch($query, $limit = 5, $offset = 0) {
        try {
            // За поръчки търсим по номер на поръчка, име на клиент, email
            $words = $this->prepareSearchWords($query);
            if (empty($words)) {
                return [];
            }

            $this->logDev("Orders::performOptimizedSearch - Query: '$query', Limit: $limit, Offset: $offset");

            // Проверяваме дали търсенето е само едно число (order_id) - ако е, връщаме САМО тази поръчка
            if (count($words) == 1 && is_numeric($words[0])) {
                $this->logDev("Orders::performOptimizedSearch - DETECTED ORDER ID SEARCH: " . $words[0]);
                $exactResults = $this->searchExactMatches($words, $limit);
                $this->logDev("Orders::performOptimizedSearch - Order ID search, exact matches only: " . count($exactResults));
                $this->logDev("Orders::performOptimizedSearch - RETURNING EARLY FOR ORDER ID: " . json_encode($exactResults));
                return array_slice($exactResults, $offset, $limit);
            }

            // Търсим точни съвпадения първо
            $exactResults = $this->searchExactMatches($words, $limit);
            $this->logDev("Orders::performOptimizedSearch - Exact matches: " . count($exactResults));

            // Ако имаме достатъчно точни резултати, връщаме ги
            if (count($exactResults) >= $limit) {
                return array_slice($exactResults, $offset, $limit);
            }

            // Търсим частични съвпадения за останалите места
            $remainingLimit = $limit - count($exactResults);
            $partialResults = $this->searchPartialMatches($words, $remainingLimit, $exactResults);
            $this->logDev("Orders::performOptimizedSearch - Partial matches: " . count($partialResults));

            // Обединяваме резултатите
            $allResults = array_merge($exactResults, $partialResults);
            $this->logDev("Orders::performOptimizedSearch - All matches: " . count($allResults));

            return array_slice($allResults, $offset, $limit);

        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * Търси точни съвпадения в поръчки с интелигентна логика
     */
    private function searchExactMatches($words, $limit) {
        $results = [];

        $language_id = $this->getLanguageId();

        // Проверяваме дали търсенето е само едно число (order_id)
        if (count($words) == 1 && is_numeric($words[0])) {
            $orderId = (int)$words[0];

            $sql = "
                SELECT DISTINCT
                    o.order_id,
                    o.firstname,
                    o.lastname,
                    o.email,
                    o.telephone,
                    o.total,
                    o.currency_code,
                    o.date_added,
                    o.order_status_id,
                    os.name as status_name,
                    'exact' as match_type
                FROM
                    " . DB_PREFIX . "order o
                LEFT JOIN
                    " . DB_PREFIX . "order_status os ON (o.order_status_id = os.order_status_id)
                WHERE
                    o.order_id = '" . $orderId . "'
                    AND os.language_id = '" . (int)$language_id . "'
                ORDER BY
                    o.date_added DESC
                LIMIT " . (int)$limit;

            $query_result = $this->db->query($sql);

            foreach ($query_result->rows as $row) {
                $results[] = [
                    'order_id' => $row['order_id'],
                    'customer_name' => trim($row['firstname'] . ' ' . $row['lastname']),
                    'email' => $row['email'] ?? '',
                    'telephone' => $row['telephone'] ?? '',
                    'total' => number_format((float)$row['total'], 2, '.', ''),
                    'currency' => $row['currency_code'] ?? '',
                    'date_added' => date('d.m.Y H:i', strtotime($row['date_added'])),
                    'status' => $row['status_name'] ?? '',
                    'order_status_id' => $row['order_status_id'] ?? 0,
                    'status_css_class' => $this->getStatusClass($row['order_status_id'] ?? 0),
                    'relevance' => 100
                ];
            }
            // Ако намерим точно съвпадение по order_id, връщаме САМО тази поръчка
            return $results;
        }

        // Проверяваме дали търсенето е email адрес
        if (count($words) == 1 && filter_var($words[0], FILTER_VALIDATE_EMAIL)) {
            $escapedEmail = $this->db->escape(mb_strtolower($words[0]));

            $sql = "
                SELECT DISTINCT
                    o.order_id,
                    o.firstname,
                    o.lastname,
                    o.email,
                    o.telephone,
                    o.total,
                    o.currency_code,
                    o.date_added,
                    o.order_status_id,
                    os.name as status_name,
                    'exact' as match_type
                FROM
                    " . DB_PREFIX . "order o
                LEFT JOIN
                    " . DB_PREFIX . "order_status os ON (o.order_status_id = os.order_status_id)
                WHERE
                    LOWER(o.email) = '" . $escapedEmail . "'
                    AND os.language_id = '" . (int)$language_id . "'
                ORDER BY
                    o.date_added DESC
                LIMIT " . (int)$limit;

            $query_result = $this->db->query($sql);

            foreach ($query_result->rows as $row) {
                $results[] = [
                    'order_id' => $row['order_id'],
                    'customer_name' => trim($row['firstname'] . ' ' . $row['lastname']),
                    'email' => $row['email'] ?? '',
                    'telephone' => $row['telephone'] ?? '',
                    'total' => number_format((float)$row['total'], 2, '.', ''),
                    'currency' => $row['currency_code'] ?? '',
                    'date_added' => date('d.m.Y H:i', strtotime($row['date_added'])),
                    'status' => $row['status_name'] ?? '',
                    'order_status_id' => $row['order_status_id'] ?? 0,
                    'status_css_class' => $this->getStatusClass($row['order_status_id'] ?? 0),
                    'relevance' => 100
                ];
            }
            // Ако намерим точно съвпадение по email, връщаме ВСИЧКИ поръчки с този email
            return $results;
        }

        // Проверяваме дали търсенето е телефонен номер (само числа или с +)
        if (count($words) == 1 && preg_match('/^[\+]?[0-9\s\-\(\)]+$/', $words[0])) {
            $phone = preg_replace('/[^\+0-9]/', '', $words[0]); // Премахваме всички символи освен + и цифри
            $escapedPhone = $this->db->escape($phone);

            $sql = "
                SELECT DISTINCT
                    o.order_id,
                    o.firstname,
                    o.lastname,
                    o.email,
                    o.telephone,
                    o.total,
                    o.currency_code,
                    o.date_added,
                    o.order_status_id,
                    os.name as status_name,
                    'exact' as match_type
                FROM
                    " . DB_PREFIX . "order o
                LEFT JOIN
                    " . DB_PREFIX . "order_status os ON (o.order_status_id = os.order_status_id)
                WHERE
                    REPLACE(REPLACE(REPLACE(REPLACE(o.telephone, ' ', ''), '-', ''), '(', ''), ')', '') = '" . $escapedPhone . "'
                    AND os.language_id = '" . (int)$language_id . "'
                ORDER BY
                    o.date_added DESC
                LIMIT " . (int)$limit;

            $query_result = $this->db->query($sql);

            foreach ($query_result->rows as $row) {
                $results[] = [
                    'order_id' => $row['order_id'],
                    'customer_name' => trim($row['firstname'] . ' ' . $row['lastname']),
                    'email' => $row['email'] ?? '',
                    'telephone' => $row['telephone'] ?? '',
                    'total' => number_format((float)$row['total'], 2, '.', ''),
                    'currency' => $row['currency_code'] ?? '',
                    'date_added' => date('d.m.Y H:i', strtotime($row['date_added'])),
                    'status' => $row['status_name'] ?? '',
                    'order_status_id' => $row['order_status_id'] ?? 0,
                    'status_css_class' => $this->getStatusClass($row['order_status_id'] ?? 0),
                    'relevance' => 100
                ];
            }
            // Ако намерим точно съвпадение по телефон, връщаме САМО поръчките с този телефон
            return $results;
        }

        return $results;
    }

    /**
     * Търси частични съвпадения в поръчки
     */
    private function searchPartialMatches($words, $limit, $excludeResults = []) {
        $results = [];
        $excludeIds = array_column($excludeResults, 'order_id');
        $language_id = $this->getLanguageId();

        // Строим условията за търсене
        $searchConditions = [];
        foreach ($words as $word) {
            $escapedWord = $this->db->escape(mb_strtolower($word));
            $transliteratedWord = $this->transliterate($word);
            $escapedTranslit = $this->db->escape(mb_strtolower($transliteratedWord));

            $wordConditions = [
                "LOWER(o.firstname) LIKE '%{$escapedWord}%'",
                "LOWER(o.lastname) LIKE '%{$escapedWord}%'",
                "LOWER(o.email) LIKE '%{$escapedWord}%'",
                "LOWER(o.telephone) LIKE '%{$escapedWord}%'",
                "LOWER(o.payment_address_1) LIKE '%{$escapedWord}%'",
                "LOWER(o.payment_address_2) LIKE '%{$escapedWord}%'",
                "LOWER(o.payment_city) LIKE '%{$escapedWord}%'",
                "LOWER(o.shipping_address_1) LIKE '%{$escapedWord}%'",
                "LOWER(o.shipping_address_2) LIKE '%{$escapedWord}%'",
                "LOWER(o.shipping_city) LIKE '%{$escapedWord}%'"
            ];

            // Добавяме транслитерирани варианти ако са различни
            if ($transliteratedWord !== $word) {
                $wordConditions[] = "LOWER(o.firstname) LIKE '%{$escapedTranslit}%'";
                $wordConditions[] = "LOWER(o.lastname) LIKE '%{$escapedTranslit}%'";
                $wordConditions[] = "LOWER(o.email) LIKE '%{$escapedTranslit}%'";
            }

            $searchConditions[] = '(' . implode(' OR ', $wordConditions) . ')';
        }

        $whereCondition = implode(' AND ', $searchConditions);

        // Добавяме изключване на вече намерените поръчки
        if (!empty($excludeIds)) {
            $excludeList = implode(',', array_map('intval', $excludeIds));
            $whereCondition .= " AND o.order_id NOT IN ({$excludeList})";
        }

        $sql = "
            SELECT DISTINCT
                o.order_id,
                o.firstname,
                o.lastname,
                o.email,
                o.telephone,
                o.total,
                o.currency_code,
                o.date_added,
                o.order_status_id,
                os.name as status_name,
                'partial' as match_type
            FROM
                " . DB_PREFIX . "order o
            LEFT JOIN
                " . DB_PREFIX . "order_status os ON (o.order_status_id = os.order_status_id)
            WHERE
                os.language_id = '" . (int)$language_id . "'
                AND ({$whereCondition})
            ORDER BY
                o.date_added DESC
            LIMIT " . (int)$limit;

        $query_result = $this->db->query($sql);

        foreach ($query_result->rows as $row) {
            $results[] = [
                'order_id' => $row['order_id'],
                'customer_name' => trim($row['firstname'] . ' ' . $row['lastname']),
                'email' => $row['email'] ?? '',
                'telephone' => $row['telephone'] ?? '',
                'total' => number_format((float)$row['total'], 2, '.', ''),
                'currency' => $row['currency_code'] ?? '',
                'date_added' => date('d.m.Y H:i', strtotime($row['date_added'])),
                'status' => $row['status_name'] ?? '',
                'order_status_id' => $row['order_status_id'] ?? 0,
                'status_css_class' => $this->getStatusClass($row['order_status_id'] ?? 0),
                'relevance' => 50
            ];
        }

        return $results;
    }

    /**
     * Търсене във всички поръчки (за "виж всички" страница)
     */
    public function searchAll($query, $page = 1, $limit = 20) {
        $offset = ($page - 1) * $limit;

        $this->logDev("Orders::searchAll - Query: {$query}, Page: {$page}, Limit: {$limit}, Offset: {$offset}");

        // Използваме същата интелигентна логика като dropdown търсенето
        $results = $this->performOptimizedSearch($query, $limit, $offset);

        $this->logDev("Orders::searchAll - Results count: " . count($results));
        if (!empty($results)) {
            $this->logDev("Orders::searchAll - First result: " . json_encode($results[0]));
        }

        // За order_id търсене, общия брой е винаги 1 или 0
        $words = $this->prepareSearchWords($query);
        if (count($words) == 1 && is_numeric($words[0])) {
            $total = count($results); // За order_id търсене броят е точно колкото намерените резултати
            $this->logDev("Orders::searchAll - Order ID search detected, total: {$total}");
        } else {
            $total = $this->getTotalCount($query);
            $this->logDev("Orders::searchAll - General search, total: {$total}");
        }

        return [
            'results' => $results,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }

    /**
     * Основна логика за търсене
     */
    private function performSearch($query, $limit = 5, $offset = 0) {
        $results = [];

        $language_id = $this->getLanguageId();

        try {
            // Подготвяме търсещите условия
            $searchConditions = $this->buildSearchConditions($query);

            $sql = "
                SELECT
                    o.order_id,
                    CONCAT(o.firstname, ' ', o.lastname) AS customer_name,
                    o.email,
                    o.telephone,
                    o.total,
                    o.currency_code,
                    o.date_added,
                    o.order_status_id,
                    os.name AS order_status,
                    CONCAT(o.shipping_address_1, ' ', o.shipping_city) AS shipping_address
                FROM
                    " . DB_PREFIX . "order o
                LEFT JOIN
                    " . DB_PREFIX . "order_status os ON (o.order_status_id = os.order_status_id)
                WHERE
                    os.language_id = '{$language_id}'
                    AND ({$searchConditions})
                ORDER BY
                    o.date_added DESC
                LIMIT {$limit}
            ";

            if ($offset > 0) {
                $sql .= " OFFSET {$offset}";
            }

            $this->logDev("Orders::performSearch - SQL: " . $sql);

            $query_result = $this->db->query($sql);
            $this->logDev("Orders::performSearch - Query result rows count: " . count($query_result->rows));

            foreach ($query_result->rows as $row) {
                $results[] = [
                    'order_id' => $row['order_id'],
                    'customer_name' => $row['customer_name'],
                    'email' => $row['email'],
                    'telephone' => $row['telephone'],
                    'total' => number_format((float)$row['total'], 2, '.', ''),
                    'currency_code' => $row['currency_code'],
                    'date_added' => date('d.m.Y H:i', strtotime($row['date_added'])),
                    'order_status' => $row['order_status'],
                    'order_status_id' => $row['order_status_id'] ?? 0,
                    'status_css_class' => $this->getStatusClass($row['order_status_id'] ?? 0),
                    'shipping_address' => $row['shipping_address']
                ];
            }

        } catch (Exception $e) {
            // Логиране на грешката
            error_log('Грешка при търсене в поръчки: ' . $e->getMessage());
        }

        return $results;
    }

    /**
     * Получаване на общия брой резултати
     */
    private function getTotalCount($query) {
        try {
            $searchConditions = $this->buildSearchConditions($query);

            $language_id = $this->getLanguageId();

            $sql = "
                SELECT COUNT(*) as total
                FROM
                    " . DB_PREFIX . "order o
                LEFT JOIN
                    " . DB_PREFIX . "order_status os ON (o.order_status_id = os.order_status_id)
                WHERE
                    os.language_id = '{$language_id}'
                    AND ({$searchConditions})
            ";

            $this->logDev("Orders::getTotalCount - SQL: " . $sql);  

            $result = $this->db->query($sql);
            $this->logDev("Orders::getTotalCount - Total: " . $result->row['total']);
            return (int)$result->row['total'];

        } catch (Exception $e) {
            error_log('Грешка при броене на поръчки: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Изграждане на търсещи условия с интелигентно търсене
     */
    private function buildSearchConditions($query) {
        $conditions = [];

        // Почистваме и разделяме заявката на думи
        $words = $this->prepareSearchWords($query);

        if (empty($words)) {
            return "1=0"; // Няма валидни думи за търсене
        }

        // За всяка дума създаваме условия
        foreach ($words as $word) {
            $wordConditions = [];

            // Оригиналната дума
            $escapedWord = $this->db->escape(mb_strtolower($word));

            // Транслитерирана версия
            $transliteratedWord = $this->transliterate($word);
            $escapedTranslit = $this->db->escape(mb_strtolower($transliteratedWord));

            // Fuzzy варианти (за думи над 4 символа)
            $fuzzyVariants = $this->generateFuzzyVariants($word);

            // Търсене в различни полета
            $textFields = ['CONCAT(o.firstname, " ", o.lastname)', 'o.email', 'os.name', 'CONCAT(o.shipping_address_1, " ", o.shipping_city)'];

            foreach ($textFields as $field) {
                $wordConditions[] = "LOWER({$field}) LIKE '%{$escapedWord}%'";

                if ($transliteratedWord !== $word) {
                    $wordConditions[] = "LOWER({$field}) LIKE '%{$escapedTranslit}%'";
                }

                // Добавяме fuzzy варианти
                foreach ($fuzzyVariants as $variant) {
                    $escapedVariant = $this->db->escape(mb_strtolower($variant));
                    $wordConditions[] = "LOWER({$field}) LIKE '%{$escapedVariant}%'";
                }
            }

            // Търсене в номер на поръчка и телефон (само за числови стойности)
            if (is_numeric($word)) {
                $wordConditions[] = "o.order_id LIKE '%{$escapedWord}%'";
                $wordConditions[] = "o.telephone LIKE '%{$escapedWord}%'";
            }

            // Обединяваме условията за тази дума с OR
            $conditions[] = '(' . implode(' OR ', $wordConditions) . ')';
        }

        // Обединяваме всички думи с AND (всички думи трябва да се намират)
        return implode(' AND ', $conditions);
    }

    /**
     * Подготвяне на думите за търсене
     */
    private function prepareSearchWords($query) {
        // Премахваме излишни интервали и разделяме на думи
        $words = preg_split('/\s+/', trim($query));

        // Филтрираме думи с дължина под 2 символа
        $words = array_filter($words, function($word) {
            return mb_strlen($word) >= 2;
        });

        return array_values($words);
    }

    /**
     * Транслитерация между кирилица и латиница
     */
    private function transliterate($text) {
        $text = mb_strtolower($text);

        // Кирилица -> Латиница
        $cyrillicToLatin = $this->translitMap;
        $result = strtr($text, $cyrillicToLatin);

        // Ако няма промяна, опитваме Латиница -> Кирилица
        if ($result === $text) {
            $latinToCyrillic = array_flip($cyrillicToLatin);

            // Специални случаи за многосимволни комбинации
            $specialCases = [
                'sht' => 'щ', 'zh' => 'ж', 'ch' => 'ч', 'sh' => 'ш', 'yu' => 'ю', 'ya' => 'я'
            ];

            // Първо заменяме специалните случаи
            foreach ($specialCases as $latin => $cyrillic) {
                $result = str_replace($latin, $cyrillic, $result);
            }

            // След това заменяме останалите символи
            $result = strtr($result, $latinToCyrillic);
        }

        return $result;
    }

    /**
     * Генериране на fuzzy варианти за толерантност към грешки
     */
    private function generateFuzzyVariants($word) {
        $variants = [];

        // Само за думи с дължина над 4 символа
        if (mb_strlen($word) <= 4) {
            return $variants;
        }

        $word = mb_strtolower($word);
        $length = mb_strlen($word);

        // Генерираме варианти с една пропусната буква
        for ($i = 0; $i < $length; $i++) {
            $variant = mb_substr($word, 0, $i) . mb_substr($word, $i + 1);
            if (mb_strlen($variant) >= 3) {
                $variants[] = $variant;
            }
        }

        // Генерираме варианти с една заменена буква (само за кирилица)
        $cyrillicChars = array_keys($this->translitMap);
        for ($i = 0; $i < $length; $i++) {
            foreach ($cyrillicChars as $char) {
                $variant = mb_substr($word, 0, $i) . $char . mb_substr($word, $i + 1);
                if ($variant !== $word) {
                    $variants[] = $variant;
                }
            }
        }

        // Ограничаваме броя на вариантите за производителност
        return array_slice(array_unique($variants), 0, 10);
    }

    /**
     * Получава статус класа за визуализация
     *
     * @param int $status_id ID на статуса
     * @return string CSS клас за статуса
     */
    private function getStatusClass($status_id) {
        $status_classes = [
            1 => 'status-new',      // Нова / Очаква обработка
            2 => 'status-processing', // В процес
            3 => 'status-sent', // Изпратена
            5 => 'status-completed',  // Завършена
            7 => 'status-cancelled',  // Отказана
            8 => 'status-rejected',  // Отхвърлена
            10 => 'status-failed',  // Провалена
            11 => 'status-returned-payment', // Върнато плащане
            12 => 'status-returned', // Върната
            15 => 'status-processed', // Обработена
            16 => 'status-reset', // Нулирана
            17 => 'status-card-payed', // Платена с карта
            18 => 'status-card-rejected', // Отхвърлено плащане с карта
            19 => 'status-delayed', // В изчакване за по-късна дата
        ];

        $class = $status_classes[$status_id] ?? 'status-unknown';
        return ' ' . $class; // Добавяме интервал в началото
    }
}
