<?php

namespace Theme25\Backend\Controller\Common\GlobalSearch;

class Categories extends \Theme25\ControllerSubMethods {


    /**
     * Транслитерационна таблица кирилица -> латиница
     */
    private $translitMap = [
        'а' => 'a', 'б' => 'b', 'в' => 'v', 'г' => 'g', 'д' => 'd', 'е' => 'e',
        'ж' => 'zh', 'з' => 'z', 'и' => 'i', 'й' => 'y', 'к' => 'k', 'л' => 'l',
        'м' => 'm', 'н' => 'n', 'о' => 'o', 'п' => 'p', 'р' => 'r', 'с' => 's',
        'т' => 't', 'у' => 'u', 'ф' => 'f', 'х' => 'h', 'ц' => 'c', 'ч' => 'ch',
        'ш' => 'sh', 'щ' => 'sht', 'ъ' => 'a', 'ь' => 'y', 'ю' => 'yu', 'я' => 'ya'
    ];

    public function __construct($registry) {
        parent::__construct($registry);
    }

    /**
     * Търсене в категории (за dropdown) с кеширане
     */
    public function search($query) {
        // Проверяваме кеша първо
        $cacheKey = $this->generateCacheKey('categories_dropdown', $query, 5);
        $cachedResults = $this->getCachedResults($cacheKey);

        if ($cachedResults !== null) {
            return $cachedResults;
        }

        // Ако няма кеширани резултати, извършваме търсенето
        $results = $this->performOptimizedSearch($query, 5);

        // Кешираме резултатите
        $this->cacheResults($cacheKey, $results);

        return $results;
    }

    /**
     * Генерира ключ за кеша
     */
    private function generateCacheKey($type, $query, $limit = null, $offset = null) {
        $keyData = [
            'type' => $type,
            'query' => mb_strtolower(trim($query)),
            'limit' => $limit,
            'offset' => $offset
        ];
        return 'search_' . md5(serialize($keyData));
    }

    /**
     * Получава кеширани резултати
     */
    private function getCachedResults($cacheKey) {
        $cacheFile = $this->cacheDir . $cacheKey . '.cache';

        if (!file_exists($cacheFile)) {
            return null;
        }

        $cacheData = file_get_contents($cacheFile);
        if ($cacheData === false) {
            return null;
        }

        $data = unserialize($cacheData);
        if ($data === false || !isset($data['timestamp']) || !isset($data['results'])) {
            return null;
        }

        // Проверяваме дали кешът е изтекъл
        if (time() - $data['timestamp'] > $this->cacheExpiry) {
            unlink($cacheFile);
            return null;
        }

        return $data['results'];
    }

    /**
     * Кешира резултатите
     */
    private function cacheResults($cacheKey, $results) {
        $cacheFile = $this->cacheDir . $cacheKey . '.cache';
        $cacheData = [
            'timestamp' => time(),
            'results' => $results
        ];

        file_put_contents($cacheFile, serialize($cacheData));
    }

    /**
     * Изчиства кеша за категории
     */
    public function clearCache() {
        if (is_dir($this->cacheDir)) {
            $files = glob($this->cacheDir . 'search_*.cache');
            foreach ($files as $file) {
                if (strpos($file, 'categories') !== false) {
                    unlink($file);
                }
            }
        }
    }

    /**
     * Оптимизирано търсене с релевантност
     */
    private function performOptimizedSearch($query, $limit = 5, $offset = 0) {
        try {
            // Подготвяме думите за търсене
            $words = $this->prepareSearchWords($query);
            if (empty($words)) {
                return [];
            }

            // Търсим точни съвпадения първо
            $exactResults = $this->searchExactMatches($words, $limit);

            // Ако имаме достатъчно точни резултати, връщаме ги
            if (count($exactResults) >= $limit) {
                return array_slice($exactResults, $offset, $limit);
            }

            // Търсим частични съвпадения за останалите места
            $remainingLimit = $limit - count($exactResults);
            $partialResults = $this->searchPartialMatches($words, $remainingLimit, $exactResults);

            // Обединяваме резултатите
            $allResults = array_merge($exactResults, $partialResults);

            return array_slice($allResults, $offset, $limit);

        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * Търсене във всички категории (за "виж всички" страница)
     */
    public function searchAll($query, $page = 1, $limit = 20) {
        // Проверяваме кеша първо
        $cacheKey = $this->generateCacheKey('categories_all', $query, $limit, ($page - 1) * $limit);
        $cachedResults = $this->getCachedResults($cacheKey);

        if ($cachedResults !== null) {
            return $cachedResults;
        }

        $offset = ($page - 1) * $limit;
        $results = $this->performOptimizedSearch($query, $limit, $offset);

        // Добавяме общия брой резултати за пагинация
        $total = $this->getTotalCount($query);

        $searchResults = [
            'results' => $results,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];

        // Кешираме резултатите
        $this->cacheResults($cacheKey, $searchResults);

        return $searchResults;
    }

    /**
     * Търси точни съвпадения в категории
     */
    private function searchExactMatches($words, $limit) {
        $language_id = $this->getLanguageId();
        $results = [];

        foreach ($words as $word) {
            $escapedWord = $this->db->escape(mb_strtolower($word));

            $sql = "
                SELECT DISTINCT
                    c.category_id,
                    cd.name,
                    c.status,
                    'exact' as match_type
                FROM
                    " . DB_PREFIX . "category c
                LEFT JOIN
                    " . DB_PREFIX . "category_description cd ON (c.category_id = cd.category_id)
                WHERE
                    cd.language_id = '" . (int)$language_id . "'
                    AND c.status = '1'
                    AND LOWER(cd.name) = '{$escapedWord}'
                ORDER BY
                    cd.name ASC
                LIMIT " . (int)$limit;

            $query_result = $this->db->query($sql);

            foreach ($query_result->rows as $row) {
                $categoryId = $row['category_id'];
                if (!isset($results[$categoryId])) {
                    $results[$categoryId] = [
                        'category_id' => $row['category_id'],
                        'name' => strip_tags(html_entity_decode($row['name'], ENT_QUOTES, 'UTF-8')),
                        'status' => $row['status'],
                        'relevance' => 100
                    ];
                }
            }

            if (count($results) >= $limit) {
                break;
            }
        }

        return array_values($results);
    }

    /**
     * Търси частични съвпадения в категории
     */
    private function searchPartialMatches($words, $limit, $excludeResults = []) {
        $language_id = $this->getLanguageId();
        $results = [];
        $excludeIds = array_column($excludeResults, 'category_id');

        // Строим условията за търсене
        $searchConditions = [];
        foreach ($words as $word) {
            $escapedWord = $this->db->escape(mb_strtolower($word));
            $transliteratedWord = $this->transliterate($word);
            $escapedTranslit = $this->db->escape(mb_strtolower($transliteratedWord));

            $wordConditions = ["LOWER(cd.name) LIKE '%{$escapedWord}%'"];

            // Добавяме транслитерирани варианти ако са различни
            if ($transliteratedWord !== $word) {
                $wordConditions[] = "LOWER(cd.name) LIKE '%{$escapedTranslit}%'";
            }

            $searchConditions[] = '(' . implode(' OR ', $wordConditions) . ')';
        }

        $whereCondition = implode(' AND ', $searchConditions);

        // Добавяме изключване на вече намерените категории
        if (!empty($excludeIds)) {
            $excludeList = implode(',', array_map('intval', $excludeIds));
            $whereCondition .= " AND c.category_id NOT IN ({$excludeList})";
        }

        $sql = "
            SELECT DISTINCT
                c.category_id,
                cd.name,
                c.status,
                'partial' as match_type
            FROM
                " . DB_PREFIX . "category c
            LEFT JOIN
                " . DB_PREFIX . "category_description cd ON (c.category_id = cd.category_id)
            WHERE
                cd.language_id = '" . (int)$language_id . "'
                AND c.status = '1'
                AND ({$whereCondition})
            ORDER BY
                cd.name ASC
            LIMIT " . (int)$limit;

        $query_result = $this->db->query($sql);

        foreach ($query_result->rows as $row) {
            $results[] = [
                'category_id' => $row['category_id'],
                'name' => strip_tags(html_entity_decode($row['name'], ENT_QUOTES, 'UTF-8')),
                'status' => $row['status'],
                'relevance' => 50
            ];
        }

        return $results;
    }

    /**
     * Основна логика за търсене
     */
    private function performSearch($query, $limit = 5, $offset = 0) {
        $results = [];

        try {
            // Подготвяме търсещите условия
            $searchConditions = $this->buildSearchConditions($query);

            $language_id = $this->getLanguageId();

            $sql = "
                SELECT
                    c.category_id,
                    cd.name,
                    cd.description,
                    c.status,
                    c.sort_order,
                    c.image
                FROM
                    " . DB_PREFIX . "category c
                LEFT JOIN
                    " . DB_PREFIX . "category_description cd ON (c.category_id = cd.category_id)
                WHERE
                    cd.language_id = '{$language_id}'
                    AND ({$searchConditions})
                ORDER BY
                    cd.name ASC
                LIMIT {$limit}
            ";

            if ($offset > 0) {
                $sql .= " OFFSET {$offset}";
            }

            $query_result = $this->db->query($sql);

            foreach ($query_result->rows as $row) {
                $results[] = [
                    'category_id' => $row['category_id'],
                    'name' => strip_tags(html_entity_decode($row['name'], ENT_QUOTES, 'UTF-8')),
                    'description' => strip_tags(html_entity_decode($row['description'], ENT_QUOTES, 'UTF-8')),
                    'status' => $row['status'],
                    'image' => $row['image']
                ];
            }

        } catch (Exception $e) {
            // Логиране на грешката
            error_log('Грешка при търсене в категории: ' . $e->getMessage());
        }

        return $results;
    }

    /**
     * Получаване на общия брой резултати
     */
    private function getTotalCount($query) {
        try {
            $searchConditions = $this->buildSearchConditions($query);
            $language_id = $this->getLanguageId();

            $sql = "
                SELECT COUNT(*) as total
                FROM
                    " . DB_PREFIX . "category c
                LEFT JOIN
                    " . DB_PREFIX . "category_description cd ON (c.category_id = cd.category_id)
                WHERE
                    cd.language_id = '{$language_id}'
                    AND ({$searchConditions})
            ";

            $result = $this->db->query($sql);
            return (int)$result->row['total'];

        } catch (Exception $e) {
            error_log('Грешка при броене на категории: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Изграждане на търсещи условия с интелигентно търсене
     */
    private function buildSearchConditions($query) {
        $conditions = [];

        // Почистваме и разделяме заявката на думи
        $words = $this->prepareSearchWords($query);

        if (empty($words)) {
            return "1=0"; // Няма валидни думи за търсене
        }

        // За всяка дума създаваме условия
        foreach ($words as $word) {
            $wordConditions = [];

            // Оригиналната дума
            $escapedWord = $this->db->escape(mb_strtolower($word));

            // Транслитерирана версия
            $transliteratedWord = $this->transliterate($word);
            $escapedTranslit = $this->db->escape(mb_strtolower($transliteratedWord));

            // Fuzzy варианти (за думи над 4 символа)
            $fuzzyVariants = $this->generateFuzzyVariants($word);

            // Търсене в различни полета
            $fields = ['cd.name', 'cd.description'];

            foreach ($fields as $field) {
                $wordConditions[] = "LOWER({$field}) LIKE '%{$escapedWord}%'";

                if ($transliteratedWord !== $word) {
                    $wordConditions[] = "LOWER({$field}) LIKE '%{$escapedTranslit}%'";
                }

                // Добавяме fuzzy варианти
                foreach ($fuzzyVariants as $variant) {
                    $escapedVariant = $this->db->escape(mb_strtolower($variant));
                    $wordConditions[] = "LOWER({$field}) LIKE '%{$escapedVariant}%'";
                }
            }

            // Обединяваме условията за тази дума с OR
            $conditions[] = '(' . implode(' OR ', $wordConditions) . ')';
        }

        // Обединяваме всички думи с AND (всички думи трябва да се намират)
        return implode(' AND ', $conditions);
    }

    /**
     * Подготвяне на думите за търсене
     */
    private function prepareSearchWords($query) {
        // Премахваме излишни интервали и разделяме на думи
        $words = preg_split('/\s+/', trim($query));

        // Филтрираме думи с дължина под 2 символа
        $words = array_filter($words, function($word) {
            return mb_strlen($word) >= 2;
        });

        return array_values($words);
    }

    /**
     * Транслитерация между кирилица и латиница
     */
    private function transliterate($text) {
        $text = mb_strtolower($text);

        // Кирилица -> Латиница
        $cyrillicToLatin = $this->translitMap;
        $result = strtr($text, $cyrillicToLatin);

        // Ако няма промяна, опитваме Латиница -> Кирилица
        if ($result === $text) {
            $latinToCyrillic = array_flip($cyrillicToLatin);

            // Специални случаи за многосимволни комбинации
            $specialCases = [
                'sht' => 'щ', 'zh' => 'ж', 'ch' => 'ч', 'sh' => 'ш', 'yu' => 'ю', 'ya' => 'я'
            ];

            // Първо заменяме специалните случаи
            foreach ($specialCases as $latin => $cyrillic) {
                $result = str_replace($latin, $cyrillic, $result);
            }

            // След това заменяме останалите символи
            $result = strtr($result, $latinToCyrillic);
        }

        return $result;
    }

    /**
     * Генериране на fuzzy варианти за толерантност към грешки
     */
    private function generateFuzzyVariants($word) {
        $variants = [];

        // Само за думи с дължина над 4 символа
        if (mb_strlen($word) <= 4) {
            return $variants;
        }

        $word = mb_strtolower($word);
        $length = mb_strlen($word);

        // Генерираме варианти с една пропусната буква
        for ($i = 0; $i < $length; $i++) {
            $variant = mb_substr($word, 0, $i) . mb_substr($word, $i + 1);
            if (mb_strlen($variant) >= 3) {
                $variants[] = $variant;
            }
        }

        // Генерираме варианти с една заменена буква (само за кирилица)
        $cyrillicChars = array_keys($this->translitMap);
        for ($i = 0; $i < $length; $i++) {
            foreach ($cyrillicChars as $char) {
                $variant = mb_substr($word, 0, $i) . $char . mb_substr($word, $i + 1);
                if ($variant !== $word) {
                    $variants[] = $variant;
                }
            }
        }

        // Ограничаваме броя на вариантите за производителност
        return array_slice(array_unique($variants), 0, 10);
    }
}
