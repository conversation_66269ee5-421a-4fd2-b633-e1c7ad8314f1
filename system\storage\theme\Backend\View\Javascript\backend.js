/**
 * Основен JavaScript модул за административния панел
 */
(function() {
    'use strict';

    // Инициализация на модула
    document.addEventListener('DOMContentLoaded', function() {
        // Инициализиране на всички компоненти
        BackendModule.init();
    });

    // Основен обект с функционалности
    var BackendModule = {
        // Конфигурация
        config: {
            userToken: new URLSearchParams(window.location.search).get('user_token') || ''
        },

        init: function() {
            this.initSidebar();
            this.initModals();
            this.initFilters();
            this.initDropdowns();
            this.initOrderDetails();
            this.initInquiries();
            this.initReviews();
            this.initCustomers();
            this.initCacheButton();
            this.initGlobalSearch();
        },

        // Функционалност на страничната лента
        initSidebar: function() {
            const toggleSidebar = document.getElementById('toggle-sidebar');
            const sidebar = document.getElementById('sidebar');
            const mobileMenu = document.getElementById('mobile-menu');

            if (toggleSidebar && sidebar) {
                toggleSidebar.addEventListener('click', function() {
                    sidebar.classList.toggle('w-64');
                    sidebar.classList.toggle('w-20');

                    const sidebarItems = document.querySelectorAll('.sidebar-item span');
                    const sidebarLogo = document.getElementById('sidebar-logo');

                    sidebarItems.forEach(item => {
                        item.classList.toggle('hidden');
                    });

                    if (sidebarLogo) {
                        sidebarLogo.classList.toggle('hidden');
                    }

                    const icon = toggleSidebar.querySelector('i');
                    if (icon) {
                        if (icon.classList.contains('ri-menu-fold-line')) {
                            icon.classList.remove('ri-menu-fold-line');
                            icon.classList.add('ri-menu-unfold-line');
                        } else {
                            icon.classList.remove('ri-menu-unfold-line');
                            icon.classList.add('ri-menu-fold-line');
                        }
                    }
                });
            }

            if (mobileMenu && sidebar) {
                mobileMenu.addEventListener('click', function() {
                    sidebar.classList.toggle('-translate-x-full');
                });
            }

            // Dark mode toggle
            const darkModeToggle = document.getElementById('dark-mode-toggle');
            if (darkModeToggle) {
                darkModeToggle.addEventListener('change', function() {
                    document.body.classList.toggle('dark');
                });
            }
        },

        // Функционалност на модалните прозорци
        initModals: function() {
            // Филтър модал
            const filterBtn = document.getElementById('filter-btn');
            const filterModal = document.getElementById('filter-modal');
            const closeFilter = document.getElementById('close-filter');
            const filterForm = document.getElementById('filter-form');
            const resetFilter = document.getElementById('reset-filter');

            if (filterBtn && filterModal && closeFilter) {
                filterBtn.addEventListener('click', function() {
                    filterModal.classList.remove('hidden');
                    document.body.style.overflow = 'hidden';
                });

                closeFilter.addEventListener('click', function() {
                    filterModal.classList.add('hidden');
                    document.body.style.overflow = 'auto';
                });

                if (filterModal) {
                    filterModal.addEventListener('click', function(e) {
                        if (e.target === filterModal) {
                            filterModal.classList.add('hidden');
                            document.body.style.overflow = 'auto';
                        }
                    });
                }

                if (filterForm) {
                    filterForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        filterModal.classList.add('hidden');
                        document.body.style.overflow = 'auto';
                    });
                }

                if (resetFilter) {
                    resetFilter.addEventListener('click', function() {
                        filterForm.reset();
                    });
                }
            }

            // Детайли на поръчка модал се инициализират в initOrderDetails
        },

        // Функционалност на филтрите
        initFilters: function() {
            // Тази функционалност вече е имплементирана в initModals
            // Оставяме метода за обратна съвместимост
        },

        // Функционалност на падащите менюта
        initDropdowns: function() {
            // Статус падащо меню
            const statusDropdownBtn = document.getElementById('status-dropdown-btn');
            const statusDropdown = document.getElementById('status-dropdown');

            if (statusDropdownBtn && statusDropdown) {
                statusDropdownBtn.addEventListener('click', function() {
                    statusDropdown.classList.toggle('hidden');
                });

                const statusItems = statusDropdown.querySelectorAll('li');
                statusItems.forEach(item => {
                    item.addEventListener('click', function() {
                        statusDropdownBtn.querySelector('span').textContent = this.textContent;
                        statusDropdown.classList.add('hidden');
                    });
                });
            }

            // Период падащо меню
            const periodDropdownBtn = document.getElementById('period-dropdown-btn');
            const periodDropdown = document.getElementById('period-dropdown');

            if (periodDropdownBtn && periodDropdown) {
                periodDropdownBtn.addEventListener('click', function() {
                    periodDropdown.classList.toggle('hidden');
                });
            }

            // Сортиране падащо меню
            const sortDropdownBtn = document.getElementById('sort-dropdown-btn');
            const sortDropdown = document.getElementById('sort-dropdown');

            if (sortDropdownBtn && sortDropdown) {
                sortDropdownBtn.addEventListener('click', function() {
                    sortDropdown.classList.toggle('hidden');
                });

                const sortItems = sortDropdown.querySelectorAll('li');
                sortItems.forEach(item => {
                    item.addEventListener('click', function() {
                        sortDropdownBtn.querySelector('span').textContent = this.textContent;
                        sortDropdown.classList.add('hidden');
                    });
                });
            }

            // Брой на страница падащо меню
            const perPageDropdownBtn = document.getElementById('per-page-dropdown-btn');
            const perPageDropdown = document.getElementById('per-page-dropdown');

            if (perPageDropdownBtn && perPageDropdown) {
                perPageDropdownBtn.addEventListener('click', function() {
                    perPageDropdown.classList.toggle('hidden');
                });

                const perPageItems = perPageDropdown.querySelectorAll('li');
                perPageItems.forEach(item => {
                    item.addEventListener('click', function() {
                        perPageDropdownBtn.querySelector('span').textContent = this.textContent;
                        perPageDropdown.classList.add('hidden');
                    });
                });
            }

            // Затваряне на падащите менюта при клик извън тях
            document.addEventListener('click', function(event) {
                if (statusDropdownBtn && statusDropdown && !statusDropdownBtn.contains(event.target) && !statusDropdown.contains(event.target)) {
                    statusDropdown.classList.add('hidden');
                }

                if (periodDropdownBtn && periodDropdown && !periodDropdownBtn.contains(event.target) && !periodDropdown.contains(event.target)) {
                    periodDropdown.classList.add('hidden');
                }

                if (sortDropdownBtn && sortDropdown && !sortDropdownBtn.contains(event.target) && !sortDropdown.contains(event.target)) {
                    sortDropdown.classList.add('hidden');
                }

                if (perPageDropdownBtn && perPageDropdown && !perPageDropdownBtn.contains(event.target) && !perPageDropdown.contains(event.target)) {
                    perPageDropdown.classList.add('hidden');
                }
            });
        },

        // Функционалност на детайлите на поръчка
        initOrderDetails: function() {
            const viewOrderBtns = document.querySelectorAll('.ri-eye-line');
            const orderDetailModal = document.getElementById('order-detail-modal');
            const closeOrderDetail = document.getElementById('close-order-detail');

            if (viewOrderBtns.length && orderDetailModal && closeOrderDetail) {
                viewOrderBtns.forEach(btn => {
                    btn.parentElement.parentElement.addEventListener('click', function() {
                        orderDetailModal.classList.remove('hidden');
                        document.body.style.overflow = 'hidden';
                    });
                });

                closeOrderDetail.addEventListener('click', function() {
                    orderDetailModal.classList.add('hidden');
                    document.body.style.overflow = 'auto';
                });

                orderDetailModal.addEventListener('click', function(e) {
                    if (e.target === orderDetailModal) {
                        orderDetailModal.classList.add('hidden');
                        document.body.style.overflow = 'auto';
                    }
                });
            }
        },

        // Функционалност на запитванията
        initInquiries: function() {
            // Модални прозорци за запитвания
            const viewModal = document.getElementById('view-modal');
            const replyModal = document.getElementById('reply-modal');
            const viewButtons = document.querySelectorAll('.view-inquiry');
            const replyButtons = document.querySelectorAll('.reply-inquiry');
            const closeButtons = document.querySelectorAll('.close-modal');
            const modalReplyBtn = document.getElementById('modal-reply-btn');
            const sendReplyBtn = document.getElementById('send-reply-btn');

            // Отваряне на модал за преглед
            if (viewButtons && viewButtons.length) {
                viewButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        // Тук трябва да се зареди информацията за запитването от сървъра
                        // Използваме data-id атрибута за идентификация на запитването
                        console.log('Преглед на запитване ID:', this.getAttribute('data-id'));
                        if (viewModal) {
                            viewModal.style.display = 'block';
                            document.body.style.overflow = 'hidden';
                        }
                    });
                });
            }

            // Отваряне на модал за отговор директно от таблицата
            if (replyButtons && replyButtons.length) {
                replyButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        // Тук трябва да се зареди информацията за запитването от сървъра
                        // Използваме data-id атрибута за идентификация на запитването
                        console.log('Отговор на запитване ID:', this.getAttribute('data-id'));
                        if (replyModal) {
                            replyModal.style.display = 'block';
                            document.body.style.overflow = 'hidden';
                        }
                    });
                });
            }

            // Отваряне на модал за отговор от модала за преглед
            if (modalReplyBtn && replyModal && viewModal) {
                modalReplyBtn.addEventListener('click', function() {
                    viewModal.style.display = 'none';
                    replyModal.style.display = 'block';
                });
            }

            // Затваряне на модалите
            if (closeButtons && closeButtons.length) {
                closeButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        if (viewModal) viewModal.style.display = 'none';
                        if (replyModal) replyModal.style.display = 'none';
                        document.body.style.overflow = 'auto';
                    });
                });
            }

            // Изпращане на отговор
            if (sendReplyBtn) {
                sendReplyBtn.addEventListener('click', function() {
                    const content = document.getElementById('reply-content');
                    if (content && content.value.trim() === '') {
                        alert('Моля, въведете съдържание на отговора.');
                        return;
                    }

                    // Тук трябва да се изпрати отговора към сървъра
                    alert('Отговорът е изпратен успешно!');

                    if (replyModal) {
                        replyModal.style.display = 'none';
                        document.body.style.overflow = 'auto';
                    }
                });
            }

            // Изтриване на запитване
            const deleteButtons = document.querySelectorAll('.delete-inquiry');
            const modalDeleteBtn = document.getElementById('modal-delete-btn');

            if (deleteButtons && deleteButtons.length) {
                deleteButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        if (confirm('Сигурни ли сте, че искате да изтриете това запитване?')) {
                            // Тук трябва да се изпрати заявка за изтриване към сървъра
                            // Използваме data-id атрибута за идентификация на запитването
                            console.log('Изтриване на запитване ID:', this.getAttribute('data-id'));
                            alert('Запитването е изтрито успешно!');
                        }
                    });
                });
            }

            if (modalDeleteBtn && viewModal) {
                modalDeleteBtn.addEventListener('click', function() {
                    if (confirm('Сигурни ли сте, че искате да изтриете това запитване?')) {
                        // Тук трябва да се изпрати заявка за изтриване към сървъра
                        viewModal.style.display = 'none';
                        document.body.style.overflow = 'auto';
                        alert('Запитването е изтрито успешно!');
                    }
                });
            }

            // Затваряне на модал при клик извън него
            window.addEventListener('click', function(event) {
                if (viewModal && event.target === viewModal) {
                    viewModal.style.display = 'none';
                    document.body.style.overflow = 'auto';
                }
                if (replyModal && event.target === replyModal) {
                    replyModal.style.display = 'none';
                    document.body.style.overflow = 'auto';
                }
            });

            // Маркиране като приключено
            const markCompletedCheckbox = document.getElementById('mark-completed');
            if (markCompletedCheckbox) {
                markCompletedCheckbox.addEventListener('change', function() {
                    if (this.checked) {
                        // Тук трябва да се изпрати заявка за промяна на статуса към сървъра
                        alert('Запитването е маркирано като приключено!');
                    }
                });
            }

            // Филтри за запитвания
            const openFilterModalBtn = document.getElementById('open-filter-modal');
            const filterModal = document.getElementById('filter-modal');
            const closeFilterModalBtns = document.querySelectorAll('.close-filter-modal');
            const applyFiltersBtn = document.getElementById('apply-filters');
            const resetFiltersBtn = document.getElementById('reset-filters');

            if (openFilterModalBtn && filterModal) {
                openFilterModalBtn.addEventListener('click', function() {
                    filterModal.style.display = 'block';
                    document.body.style.overflow = 'hidden';
                });
            }

            if (closeFilterModalBtns && closeFilterModalBtns.length) {
                closeFilterModalBtns.forEach(btn => {
                    btn.addEventListener('click', function() {
                        if (filterModal) {
                            filterModal.style.display = 'none';
                            document.body.style.overflow = 'auto';
                        }
                    });
                });
            }

            if (applyFiltersBtn && filterModal) {
                applyFiltersBtn.addEventListener('click', function() {
                    // Тук трябва да се приложат филтрите
                    filterModal.style.display = 'none';
                    document.body.style.overflow = 'auto';
                });
            }

            if (resetFiltersBtn) {
                resetFiltersBtn.addEventListener('click', function() {
                    const filterInputs = document.querySelectorAll('#filter-modal input, #filter-modal select');
                    filterInputs.forEach(input => {
                        input.value = '';
                    });
                });
            }
        },

        // Функционалност на отзивите
        initReviews: function() {
            // Филтър за отзиви
            const filterButton = document.getElementById('filter-button');
            const filterDropdown = document.getElementById('filter-dropdown');

            if (filterButton && filterDropdown) {
                filterButton.addEventListener('click', function() {
                    filterDropdown.classList.toggle('hidden');
                });

                // Затваряне на филтъра при клик извън него
                document.addEventListener('click', function(event) {
                    if (!filterButton.contains(event.target) && !filterDropdown.contains(event.target)) {
                        filterDropdown.classList.add('hidden');
                    }
                });
            }

            // Отваряне на детайли за отзив
            const reviewRows = document.querySelectorAll('.review-row');

            if (reviewRows && reviewRows.length) {
                reviewRows.forEach(row => {
                    row.addEventListener('click', function() {
                        const reviewId = this.getAttribute('data-review-id');
                        // Тук трябва да се зареди информацията за отзива от сървъра
                        // и да се покаже в модален прозорец или да се навигира към страница с детайли
                        alert('Отваряне на детайли за отзив #' + reviewId);
                    });
                });
            }

            // Експорт на отзиви
            const exportButton = document.getElementById('export-button');

            if (exportButton) {
                exportButton.addEventListener('click', function() {
                    // Тук трябва да се имплементира функционалност за експорт на отзиви
                    alert('Експортиране на отзиви...');
                });
            }

            // Звездна оценка в модалния прозорец за добавяне/редактиране на отзив
            const ratingInputs = document.querySelectorAll('.star-rating input');

            if (ratingInputs && ratingInputs.length) {
                ratingInputs.forEach(input => {
                    input.addEventListener('change', function() {
                        // Актуализиране на визуалната оценка
                        const rating = this.value;
                        console.log('Избрана оценка: ' + rating);
                    });
                });
            }
        },

        // Функционалност за управление на клиенти
        initCustomers: function() {
            // Филтър модал се инициализира в initModals
            // Тук само добавяме специфична функционалност за клиенти

            // Падащите менюта се инициализират в initDropdowns
            // Тук само добавяме специфична функционалност за клиенти

            // Експорт на клиенти
            const exportBtn = document.getElementById('export-btn');

            if (exportBtn) {
                exportBtn.addEventListener('click', function() {
                    // Тук трябва да се имплементира функционалност за експорт на клиенти
                    alert('Експортиране на клиенти...');
                });
            }

            // Избиране на всички клиенти
            const selectAllCheckbox = document.querySelector('thead input[type="checkbox"]');
            const clientCheckboxes = document.querySelectorAll('tbody input[type="checkbox"]');

            if (selectAllCheckbox && clientCheckboxes.length) {
                selectAllCheckbox.addEventListener('change', function() {
                    clientCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                });

                clientCheckboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        const allChecked = Array.from(clientCheckboxes).every(cb => cb.checked);
                        const someChecked = Array.from(clientCheckboxes).some(cb => cb.checked);

                        selectAllCheckbox.checked = allChecked;
                        selectAllCheckbox.indeterminate = someChecked && !allChecked;
                    });
                });
            }
        },

        // Функционалност за кеш бутона
        initCacheButton: function() {
            // Търсим основния кеш бутон който съдържа текст "Кеш"
            const cacheButton = Array.from(document.querySelectorAll('button')).find(btn =>
                btn.textContent.trim().includes('Кеш') && btn.querySelector('i.ri-refresh-line')
            );

            if (cacheButton) {
                cacheButton.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Показваме индикатор за зареждане
                    const originalText = this.innerHTML;
                    this.disabled = true;
                    this.innerHTML = '<div class="w-5 h-5 flex items-center justify-center mr-2"><i class="ri-loader-4-line ri-spin"></i></div>Изтриване...';

                    // AJAX заявка за изтриване на кеша
                    fetch('index.php?route=common/cache/clear&user_token=' + BackendModule.config.userToken, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Cache-Control': 'no-cache'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            BackendModule.showAlert('success', data.success);
                        } else if (data.error) {
                            BackendModule.showAlert('error', data.error);
                        } else {
                            BackendModule.showAlert('success', 'Кешът е изтрит успешно!');
                        }
                    })
                    .catch(error => {
                        console.error('Грешка при изтриване на кеша:', error);
                        BackendModule.showAlert('error', 'Възникна грешка при изтриване на кеша');
                    })
                    .finally(() => {
                        // Връщаме бутона в първоначално състояние
                        this.disabled = false;
                        this.innerHTML = originalText;
                    });
                });
            }
        },

        // Функционалност за глобалната търсачка
        initGlobalSearch: function() {
            const searchInput = document.querySelector('.search-input');
            const searchButton = searchInput ? searchInput.nextElementSibling : null;

            if (!searchInput) return;

            let searchTimeout;
            let searchDropdown;
            let currentRequest;

            // Създаване на dropdown за резултатите
            function createSearchDropdown() {
                if (searchDropdown) return searchDropdown;

                searchDropdown = document.createElement('div');
                searchDropdown.className = 'absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-b-lg shadow-lg z-50 hidden max-h-96 overflow-y-auto';
                searchInput.parentElement.appendChild(searchDropdown);
                searchInput.parentElement.style.position = 'relative';

                return searchDropdown;
            }

            // Функция за търсене
            function performSearch(query) {
                if (!query || query.length < 2) {
                    hideSearchResults();
                    return;
                }

                // Отменяме предишната заявка ако има такава
                if (currentRequest) {
                    currentRequest.abort();
                }

                const controller = new AbortController();
                currentRequest = controller;

                // Показваме индикатор за зареждане
                showSearchLoading();

                const timestamp = new Date().getTime();
                fetch(`index.php?route=common/globalsearch/search&query=${encodeURIComponent(query)}&user_token=${BackendModule.config.userToken}&_=${timestamp}`, {
                    signal: controller.signal,
                    headers: { 'Cache-Control': 'no-cache' }
                })
                .then(response => {
                    if (!response.ok) throw new Error('Грешка при търсене');
                    return response.json();
                })
                .then(data => {
                    if (controller.signal.aborted) return;
                    showSearchResults(data, query);
                })
                .catch(error => {
                    if (error.name !== 'AbortError') {
                        console.error('Грешка при търсене:', error);
                        showSearchError();
                    }
                });
            }

            // Показване на индикатор за зареждане
            function showSearchLoading() {
                const dropdown = createSearchDropdown();
                dropdown.innerHTML = '<div class="p-4 text-center text-gray-500"><i class="ri-loader-4-line ri-spin mr-2"></i>Търсене...</div>';
                dropdown.classList.remove('hidden');
            }

            // Показване на резултатите
            function showSearchResults(data, query) {
                const dropdown = createSearchDropdown();

                if (!data || (!data.products && !data.categories && !data.orders && !data.customers)) {
                    dropdown.innerHTML = `<div class="p-4 text-center text-gray-500">Няма намерени резултати за '${query}'</div>`;
                    dropdown.classList.remove('hidden');
                    return;
                }

                let html = '';

                // Продукти
                if (data.products && data.products.length > 0) {
                    html += '<div class="border-b border-gray-200">';
                    html += '<div class="p-3 bg-gray-50 font-medium text-gray-700 flex items-center justify-between">';
                    html += '<span><i class="ri-shopping-bag-line mr-2"></i>Продукти</span>';
                    html += `<button onclick="BackendModule.openSearchResults('products', '${encodeURIComponent(query)}')" class="text-xs text-blue-600 hover:text-blue-800">Виж всички</button>`;
                    html += '</div>';
                    data.products.forEach(item => {
                        html += `<a href="index.php?route=catalog/product/edit&product_id=${item.product_id}&user_token=${BackendModule.config.userToken}" class="block p-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0">`;
                        html += `<div class="font-medium">${item.name}</div>`;
                        html += `<div class="text-sm text-gray-500">Модел: ${item.model || 'N/A'} | Цена: ${item.price || '0.00'} лв.</div>`;
                        html += '</a>';
                    });
                    html += '</div>';
                }

                // Категории
                if (data.categories && data.categories.length > 0) {
                    html += '<div class="border-b border-gray-200">';
                    html += '<div class="p-3 bg-gray-50 font-medium text-gray-700 flex items-center justify-between">';
                    html += '<span><i class="ri-folder-line mr-2"></i>Категории</span>';
                    html += `<button onclick="BackendModule.openSearchResults('categories', '${encodeURIComponent(query)}')" class="text-xs text-blue-600 hover:text-blue-800">Виж всички</button>`;
                    html += '</div>';
                    data.categories.forEach(item => {
                        html += `<a href="index.php?route=catalog/category/edit&category_id=${item.category_id}&user_token=${BackendModule.config.userToken}" class="block p-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0">`;
                        html += `<div class="font-medium">${item.name}</div>`;
                        html += '</a>';
                    });
                    html += '</div>';
                }

                // Поръчки
                if (data.orders && data.orders.length > 0) {
                    html += '<div class="border-b border-gray-200">';
                    html += '<div class="p-3 bg-gray-50 font-medium text-gray-700 flex items-center justify-between">';
                    html += '<span><i class="ri-shopping-cart-line mr-2"></i>Поръчки</span>';
                    html += `<button onclick="BackendModule.openSearchResults('orders', '${encodeURIComponent(query)}')" class="text-xs text-blue-600 hover:text-blue-800">Виж всички</button>`;
                    html += '</div>';
                    data.orders.forEach(item => {
                        html += `<a href="index.php?route=sale/order/info&order_id=${item.order_id}&user_token=${BackendModule.config.userToken}" class="block p-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0">`;
                        html += `<div class="font-medium">Поръчка #${item.order_id} - ${item.customer_name}</div>`;
                        html += `<div class="text-sm text-gray-500">${item.total} ${item.currency} | ${item.date_added} | <span class="${item.status_css_class || ''}">${item.status || 'Неизвестен статус'}</span></div>`;
                        html += '</a>';
                    });
                    html += '</div>';
                }

                // Клиенти
                if (data.customers && data.customers.length > 0) {
                    html += '<div class="border-b border-gray-200">';
                    html += '<div class="p-3 bg-gray-50 font-medium text-gray-700 flex items-center justify-between">';
                    html += '<span><i class="ri-user-line mr-2"></i>Клиенти</span>';
                    html += `<button onclick="BackendModule.openSearchResults('customers', '${encodeURIComponent(query)}')" class="text-xs text-blue-600 hover:text-blue-800">Виж всички</button>`;
                    html += '</div>';
                    data.customers.forEach(item => {
                        html += `<a href="index.php?route=customer/customer/edit&customer_id=${item.customer_id}&user_token=${BackendModule.config.userToken}" class="block p-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0">`;
                        html += `<div class="font-medium">${item.name || 'Неизвестен клиент'}</div>`;
                        html += `<div class="text-sm text-gray-500">${item.email} | ${item.telephone || 'N/A'} | ${item.date_added}</div>`;
                        html += '</a>';
                    });
                    html += '</div>';
                }

                // Добавяме бутон за всички резултати в края
                if (html) {
                    html += '<div class="border-t border-gray-200 p-3 bg-gray-50">';
                    html += `<a href="index.php?route=common/globalsearch&query=${encodeURIComponent(query)}&user_token=${BackendModule.config.userToken}" class="block w-full text-center py-2 px-4 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">`;
                    html += '<i class="ri-search-line mr-2"></i>Всички резултати';
                    html += '</a>';
                    html += '</div>';
                }

                dropdown.innerHTML = html;
                dropdown.classList.remove('hidden');
            }

            // Показване на грешка
            function showSearchError() {
                const dropdown = createSearchDropdown();
                dropdown.innerHTML = '<div class="p-4 text-center text-red-500">Възникна грешка при търсене</div>';
                dropdown.classList.remove('hidden');
            }

            // Скриване на резултатите
            function hideSearchResults() {
                if (searchDropdown) {
                    searchDropdown.classList.add('hidden');
                }
            }

            // Event listeners
            searchInput.addEventListener('input', function() {
                const query = this.value.trim();

                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    performSearch(query);
                }, 300); // debounce 300ms
            });

            searchInput.addEventListener('focus', function() {
                const query = this.value.trim();
                if (query.length >= 2) {
                    performSearch(query);
                }
            });

            // Затваряне на dropdown при клик извън него
            document.addEventListener('click', function(e) {
                if (!searchInput.contains(e.target) && !searchDropdown?.contains(e.target)) {
                    hideSearchResults();
                }
            });

            // Търсене при натискане на бутона
            if (searchButton) {
                searchButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    const query = searchInput.value.trim();
                    if (query.length >= 2) {
                        performSearch(query);
                    }
                });
            }
        },

        // Отваряне на страница с всички резултати от търсенето
        openSearchResults: function(type, query) {
            const url = `index.php?route=common/globalsearch/${type}&query=${query}&user_token=${BackendModule.config.userToken}`;
            window.open(url, '_blank');
        },

        // Изчистване на кеша на търсачката
        clearSearchCache: function() {
            fetch(`index.php?route=common/globalsearch/clearCache&user_token=${BackendModule.config.userToken}`, {
                method: 'POST',
                headers: { 'Cache-Control': 'no-cache' }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('Search cache cleared successfully');
                    BackendModule.showAlert('success', 'Кешът за търсене е изчистен успешно!');
                } else {
                    console.error('Failed to clear search cache:', data.error);
                    BackendModule.showAlert('error', 'Грешка при изчистване на кеша за търсене');
                }
            })
            .catch(error => {
                console.error('Error clearing search cache:', error);
                BackendModule.showAlert('error', 'Възникна грешка при изчистване на кеша');
            });
        },

        // Показване на съобщение (използва същия метод като в product-form.js)
        showAlert: function(type, message, duration = 3) {
            // Премахване на съществуващи съобщения
            const existingAlert = document.querySelector('.alert-message');
            if (existingAlert) {
                existingAlert.remove();
            }

            // Създаване на новото съобщение
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert-message fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
                type === 'success' ? 'bg-green-100 text-green-800 border-l-4 border-green-500' :
                type === 'error' ? 'bg-red-100 text-red-800 border-l-4 border-red-500' :
                'bg-blue-100 text-blue-800 border-l-4 border-blue-500'
            } flex items-center space-x-3`;

            // Икона според типа
            const iconClass =
                type === 'success' ? 'ri-check-line' :
                type === 'error' ? 'ri-error-warning-line' :
                'ri-information-line';

            alertDiv.innerHTML = `
                <div class="flex-shrink-0">
                    <i class="${iconClass}"></i>
                </div>
                <div>${message}</div>
                <button class="ml-auto text-gray-500 hover:text-gray-700" id="close-alert">
                    <i class="ri-close-line"></i>
                </button>
            `;

            document.body.appendChild(alertDiv);

            // Бутон за затваряне
            const closeButton = alertDiv.querySelector('#close-alert');
            closeButton.addEventListener('click', () => alertDiv.remove());

            // Автоматично затваряне след определено време
            if (duration > 0) {
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, duration * 1000);
            }
        },

        // Помощни функции
        helpers: {
            getUrlVars: function() {
                var vars = [], hash;
                var hashes = window.location.href.slice(window.location.href.indexOf('?') + 1).split('&');
                for(var i = 0; i < hashes.length; i++) {
                    hash = hashes[i].split('=');
                    vars.push(hash[0]);
                    vars[hash[0]] = hash[1];
                }
                return vars;
            },

            getUrlVar: function(name) {
                return this.getUrlVars()[name];
            }
        }
    };

    // Експортиране на модула за глобален достъп
    window.BackendModule = BackendModule;

})();