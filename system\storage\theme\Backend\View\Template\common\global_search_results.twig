<!-- Search Results Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
    <div class="flex flex-col md:flex-row md:items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-800 flex items-center">
                <i class="ri-search-line mr-2"></i>
                Резултати от търсене: "{{ query }}"
            </h1>
            <p class="text-gray-500 mt-1">{{ title }}</p>
        </div>
        <div class="mt-4 md:mt-0 flex items-center space-x-2">
            <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">{{ title }}</span>
            <span class="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm font-medium">{{ total }} резултата</span>
        </div>
    </div>
</div>

<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto bg-gray-50 p-6">
    {% if error %}
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div class="flex items-center">
                <i class="ri-error-warning-line text-red-500 mr-2"></i>
                <span class="text-red-700">{{ error }}</span>
            </div>
        </div>
    {% elseif results %}
        <!-- Резултати за продукти -->
        {% if type == 'products' %}
            <div class="bg-white rounded shadow">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <div class="flex items-center">
                                        <input type="checkbox" class="mr-2">
                                        ID
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Име
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Модел
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    SKU
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Цена
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Статус
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Действия
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for product in results %}
                            <tr class="hover:bg-gray-50" data-product-id="{{ product.product_id }}">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <input type="checkbox" name="selected[]" value="{{ product.product_id }}" class="mr-2">
                                        <span class="text-sm font-medium text-gray-900">{{ product.product_id }}</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm font-medium text-gray-900">{{ product.name }}</div>
                                    {% if product.description %}
                                    <div class="text-sm text-gray-500">{{ product.description|slice(0, 100) }}...</div>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ product.model }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ product.sku }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ product.price }} лв.</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if product.status == '1' %}
                                        <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">Активен</span>
                                    {% else %}
                                        <span class="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">Неактивен</span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="index.php?route=catalog/product/edit&product_id={{ product.product_id }}&user_token={{ user_token }}"
                                       class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                        <i class="ri-edit-line mr-1"></i>
                                        Редактирай
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        {% endif %}

        <!-- Резултати за категории -->
        {% if type == 'categories' %}
            <div class="bg-white rounded shadow">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <div class="flex items-center">
                                        <input type="checkbox" class="mr-2">
                                        ID
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Име
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Описание
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Статус
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Действия
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for category in results %}
                            <tr class="hover:bg-gray-50" data-category-id="{{ category.category_id }}">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <input type="checkbox" name="selected[]" value="{{ category.category_id }}" class="mr-2">
                                        <span class="text-sm font-medium text-gray-900">{{ category.category_id }}</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm font-medium text-gray-900">{{ category.name }}</div>
                                </td>
                                <td class="px-6 py-4">
                                    {% if category.description %}
                                    <div class="text-sm text-gray-900">{{ category.description|slice(0, 150) }}...</div>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if category.status == '1' %}
                                        <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">Активна</span>
                                    {% else %}
                                        <span class="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">Неактивна</span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="index.php?route=catalog/category/edit&category_id={{ category.category_id }}&user_token={{ user_token }}"
                                       class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                        <i class="ri-edit-line mr-1"></i>
                                        Редактирай
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        {% endif %}

        <!-- Резултати за поръчки -->
        {% if type == 'orders' %}
            <div class="bg-white rounded shadow">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <div class="flex items-center">
                                        <input type="checkbox" class="mr-2">
                                        Номер
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Дата
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Клиент
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Телефон
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Статус
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Сума
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Действия
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for order in results %}
                            <tr class="hover:bg-gray-50" data-order-id="{{ order.order_id }}">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <input type="checkbox" name="selected[]" value="{{ order.order_id }}" class="mr-2">
                                        <span class="text-sm font-medium text-gray-900">#{{ order.order_id }}</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ order.date_added }}</div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm font-medium text-gray-900">{{ order.customer_name }}</div>
                                    {% if order.email %}
                                    <div class="text-sm text-gray-500">{{ order.email }}</div>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ order.telephone }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full{{ order.status_css_class|default(' bg-blue-100 text-blue-800') }}">{{ order.status|default('Неизвестен статус') }}</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {{ order.total }} {{ order.currency }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="index.php?route=sale/order/info&order_id={{ order.order_id }}&user_token={{ user_token }}"
                                       class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                        <i class="ri-eye-line mr-1"></i>
                                        Преглед
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        {% endif %}

        <!-- Резултати за клиенти -->
        {% if type == 'customers' %}
            <div class="bg-white rounded shadow">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <div class="flex items-center">
                                        <input type="checkbox" class="mr-2">
                                        ID
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Име
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Email
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Телефон
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Група
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Статус
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Дата на регистрация
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Действия
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for customer in results %}
                            <tr class="hover:bg-gray-50" data-customer-id="{{ customer.customer_id }}">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <input type="checkbox" name="selected[]" value="{{ customer.customer_id }}" class="mr-2">
                                        <span class="text-sm font-medium text-gray-900">{{ customer.customer_id }}</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm font-medium text-gray-900">{{ customer.name }}</div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900">{{ customer.email }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ customer.telephone }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ customer.customer_group }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if customer.status == 'Активен' %}
                                        <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">Активен</span>
                                    {% else %}
                                        <span class="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">Неактивен</span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ customer.date_added }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="index.php?route=customer/customer/edit&customer_id={{ customer.customer_id }}&user_token={{ user_token }}"
                                       class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                        <i class="ri-edit-line mr-1"></i>
                                        Редактирай
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        {% endif %}

        <!-- Пагинация -->
        {% if pagination %}
            <div class="mt-6">
                {{ pagination|raw }}
            </div>
        {% endif %}
    {% else %}
        <div class="bg-white rounded shadow p-12 text-center">
            <i class="ri-search-line text-gray-400 text-6xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Няма намерени резултати</h3>
            <p class="text-gray-500">Опитайте с различни ключови думи</p>
        </div>
    {% endif %}
</main>
