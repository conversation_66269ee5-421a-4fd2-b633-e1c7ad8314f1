{{ header }}{{ sidebar }}

<!-- Search Results Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
    <div class="flex flex-col md:flex-row md:items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-800 flex items-center">
                <i class="ri-search-line mr-2"></i>
                Резултати от търсене: "{{ query }}"
            </h1>
            <p class="text-gray-500 mt-1">Всички резултати</p>
        </div>
        <div class="mt-4 md:mt-0 flex items-center space-x-2">
            <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">Всички групи</span>
            <span class="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm font-medium">{{ total }} резултата</span>
        </div>
    </div>
</div>

<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto bg-gray-50 p-6">
    {% if error %}
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div class="flex items-center">
                <i class="ri-error-warning-line text-red-500 mr-2"></i>
                <span class="text-red-700">{{ error }}</span>
            </div>
        </div>
    {% elseif results %}
        <!-- Групови линкове -->
        {% if group_counts %}
            <div class="bg-white rounded-lg shadow mb-6 p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Резултати по групи</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {% if group_counts.products %}
                        <a href="index.php?route=common/globalsearch/products&query={{ query|url_encode }}&user_token={{ user_token }}" 
                           class="flex items-center p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors">
                            <i class="ri-shopping-bag-line text-blue-600 text-2xl mr-3"></i>
                            <div>
                                <div class="font-medium text-blue-900">Продукти</div>
                                <div class="text-sm text-blue-600">{{ group_counts.products }} резултата</div>
                            </div>
                        </a>
                    {% endif %}
                    {% if group_counts.categories %}
                        <a href="index.php?route=common/globalsearch/categories&query={{ query|url_encode }}&user_token={{ user_token }}" 
                           class="flex items-center p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors">
                            <i class="ri-folder-line text-green-600 text-2xl mr-3"></i>
                            <div>
                                <div class="font-medium text-green-900">Категории</div>
                                <div class="text-sm text-green-600">{{ group_counts.categories }} резултата</div>
                            </div>
                        </a>
                    {% endif %}
                    {% if group_counts.orders %}
                        <a href="index.php?route=common/globalsearch/orders&query={{ query|url_encode }}&user_token={{ user_token }}" 
                           class="flex items-center p-4 bg-orange-50 border border-orange-200 rounded-lg hover:bg-orange-100 transition-colors">
                            <i class="ri-shopping-cart-line text-orange-600 text-2xl mr-3"></i>
                            <div>
                                <div class="font-medium text-orange-900">Поръчки</div>
                                <div class="text-sm text-orange-600">{{ group_counts.orders }} резултата</div>
                            </div>
                        </a>
                    {% endif %}
                    {% if group_counts.customers %}
                        <a href="index.php?route=common/globalsearch/customers&query={{ query|url_encode }}&user_token={{ user_token }}" 
                           class="flex items-center p-4 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors">
                            <i class="ri-user-line text-purple-600 text-2xl mr-3"></i>
                            <div>
                                <div class="font-medium text-purple-900">Клиенти</div>
                                <div class="text-sm text-purple-600">{{ group_counts.customers }} резултата</div>
                            </div>
                        </a>
                    {% endif %}
                </div>
            </div>
        {% endif %}

        <!-- Смесени резултати -->
        {% if results %}
            <div class="bg-white rounded shadow">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Тип
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    ID
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Име/Заглавие
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Детайли
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Действия
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for result in results %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if result.result_type == 'products' %}
                                        <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                                            <i class="ri-shopping-bag-line mr-1"></i>Продукт
                                        </span>
                                    {% elseif result.result_type == 'categories' %}
                                        <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                                            <i class="ri-folder-line mr-1"></i>Категория
                                        </span>
                                    {% elseif result.result_type == 'orders' %}
                                        <span class="px-2 py-1 text-xs font-medium rounded-full bg-orange-100 text-orange-800">
                                            <i class="ri-shopping-cart-line mr-1"></i>Поръчка
                                        </span>
                                    {% elseif result.result_type == 'customers' %}
                                        <span class="px-2 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800">
                                            <i class="ri-user-line mr-1"></i>Клиент
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="text-sm font-medium text-gray-900">
                                        {% if result.result_type == 'products' %}
                                            {{ result.product_id }}
                                        {% elseif result.result_type == 'categories' %}
                                            {{ result.category_id }}
                                        {% elseif result.result_type == 'orders' %}
                                            #{{ result.order_id }}
                                        {% elseif result.result_type == 'customers' %}
                                            {{ result.customer_id }}
                                        {% endif %}
                                    </span>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm font-medium text-gray-900">
                                        {% if result.result_type == 'orders' %}
                                            {{ result.customer_name|default('Неизвестен клиент') }}
                                        {% else %}
                                            {{ result.name }}
                                        {% endif %}
                                    </div>
                                    {% if result.result_type == 'products' and result.description %}
                                        <div class="text-sm text-gray-500">{{ result.description|slice(0, 100) }}...</div>
                                    {% elseif result.result_type == 'orders' and result.email %}
                                        <div class="text-sm text-gray-500">{{ result.email }}</div>
                                    {% elseif result.result_type == 'customers' and result.email %}
                                        <div class="text-sm text-gray-500">{{ result.email }}</div>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4">
                                    {% if result.result_type == 'products' %}
                                        <div class="text-sm text-gray-900">Модел: {{ result.model|default('N/A') }}</div>
                                        <div class="text-sm text-gray-500">Цена: {{ result.price|default('0.00') }} лв.</div>
                                    {% elseif result.result_type == 'orders' %}
                                        <div class="text-sm text-gray-900">{{ result.total }} {{ result.currency }}</div>
                                        <div class="text-sm text-gray-500">{{ result.date_added }}</div>
                                    {% elseif result.result_type == 'customers' %}
                                        <div class="text-sm text-gray-900">{{ result.telephone|default('N/A') }}</div>
                                        <div class="text-sm text-gray-500">{{ result.date_added }}</div>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    {% if result.result_type == 'products' %}
                                        <a href="index.php?route=catalog/product/edit&product_id={{ result.product_id }}&user_token={{ user_token }}"
                                           class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                            <i class="ri-edit-line mr-1"></i>
                                            Редактирай
                                        </a>
                                    {% elseif result.result_type == 'categories' %}
                                        <a href="index.php?route=catalog/category/edit&category_id={{ result.category_id }}&user_token={{ user_token }}"
                                           class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                            <i class="ri-edit-line mr-1"></i>
                                            Редактирай
                                        </a>
                                    {% elseif result.result_type == 'orders' %}
                                        <a href="index.php?route=sale/order/info&order_id={{ result.order_id }}&user_token={{ user_token }}"
                                           class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                            <i class="ri-eye-line mr-1"></i>
                                            Преглед
                                        </a>
                                    {% elseif result.result_type == 'customers' %}
                                        <a href="index.php?route=customer/customer/edit&customer_id={{ result.customer_id }}&user_token={{ user_token }}"
                                           class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                            <i class="ri-edit-line mr-1"></i>
                                            Редактирай
                                        </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        {% endif %}

        <!-- Пагинация -->
        {% if pagination %}
            <div class="mt-6">
                {{ pagination|raw }}
            </div>
        {% endif %}
    {% else %}
        <div class="bg-white rounded shadow p-12 text-center">
            <i class="ri-search-line text-gray-400 text-6xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Няма намерени резултати</h3>
            <p class="text-gray-500">Опитайте с различни ключови думи</p>
        </div>
    {% endif %}
</main>

{{ footer }}
