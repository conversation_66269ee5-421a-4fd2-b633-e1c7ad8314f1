<?php

namespace Theme25\Backend\Controller\Common;

class GlobalSearch extends \Theme25\Controller {

    protected $cacheDir = 'search/';
    
    /**
     * Време на валидност на кеша в секунди (1 час)
     */
    protected $cacheExpiry = 3600;

    public function __construct($registry) {
        parent::__construct($registry, 'common/globalsearch');
        $this->prepareLanguageData();
        $this->setCacheDir();
    }

    /**
     * Главен endpoint за глобалното търсене
     */
    public function index() {
        $json = [];
        
        ob_start();

        $query = $this->requestGet('query');

        $this->logDev("GlobalSearch::index - Query: " . $query);
        
        if (!$query || strlen(trim($query)) < 2) {
            $json['error'] = 'Търсеният израз трябва да бъде поне 2 символа';
        } else {
            // Проверяваме дали търсенето е число за интелигентно приоритизиране
            $isNumericSearch = is_numeric(trim($query));
            $this->logDev("GlobalSearch::index - Is numeric search: " . ($isNumericSearch ? 'yes' : 'no'));

            // Зареждане на под-контролерите за различните типове търсене
            $results = [];

            // При числово търсене приоритизираме поръчките
            if ($isNumericSearch) {
                // Първо търсим в поръчки (най-висок приоритет за числа)
                $orderController = $this->setBackendSubController('Common/GlobalSearch/Orders', $this);
                if ($orderController) {
                    $orderResults = $orderController->search($query);
                    if (!empty($orderResults)) {
                        $results['orders'] = $orderResults;
                        $this->logDev("GlobalSearch::index - Found order results for numeric search");
                    }
                }

                // Ако намерим поръчка, търсим клиента който я е направил
                if (!empty($results['orders'])) {
                    $this->logDev("GlobalSearch::index - Searching for customer of found order");
                    // Вземаме първата поръчка и търсим нейния клиент
                    $firstOrder = $results['orders'][0];
                    if (!empty($firstOrder['email'])) {
                        $customerController = $this->setBackendSubController('Common/GlobalSearch/Customers', $this);
                        if ($customerController) {
                            $customerResults = $customerController->search($firstOrder['email']);
                            if (!empty($customerResults)) {
                                $results['customers'] = $customerResults;
                                $this->logDev("GlobalSearch::index - Found customer for order email: " . $firstOrder['email']);
                            }
                        }
                    }
                } else {
                    // Ако няма поръчки, търсим в продукти
                    $productController = $this->setBackendSubController('Common/GlobalSearch/Products', $this);
                    if ($productController) {
                        $productResults = $productController->search($query);
                        if (!empty($productResults)) {
                            $results['products'] = $productResults;
                        }
                    }
                }
            } else {
                // При не-числово търсене използваме стандартния ред
                // Търсене в продукти
                $productController = $this->setBackendSubController('Common/GlobalSearch/Products', $this);

                $this->logDev("GlobalSearch::index - Product controller: " . get_class($productController));

                if ($productController) {
                    $productResults = $productController->search($query);

                    $this->logDev("GlobalSearch::index - Product results: " . print_r($productResults, true));

                    if (!empty($productResults)) {
                        $results['products'] = $productResults;
                    }
                }

                // Търсене в категории
                $categoryController = $this->setBackendSubController('Common/GlobalSearch/Categories', $this);
                if ($categoryController) {
                    $categoryResults = $categoryController->search($query);
                    if (!empty($categoryResults)) {
                        $results['categories'] = $categoryResults;
                    }
                }

                // Търсене в поръчки (само ако не е числово търсене)
                $orderController = $this->setBackendSubController('Common/GlobalSearch/Orders', $this);
                if ($orderController) {
                    $orderResults = $orderController->search($query);
                    if (!empty($orderResults)) {
                        $results['orders'] = $orderResults;
                    }
                }

                // Търсене в клиенти
                $customerController = $this->setBackendSubController('Common/GlobalSearch/Customers', $this);
                if ($customerController) {
                    $customerResults = $customerController->search($query);
                    if (!empty($customerResults)) {
                        $results['customers'] = $customerResults;
                    }
                }
            }
            
            $json = $results;
        }
        
        $output = ob_get_clean();
        if ($output) {
            $json['error'] = $output;
        }

        $this->logDev("GlobalSearch::index - Final results: " . print_r($json, true));
        
        $this->setJSONResponseOutput($json);
    }

    /**
     * Търсене във всички продукти
     */
    public function products() {
        $query = $this->requestGet('query');
        $page = (int)$this->requestGet('page', 1);
        $limit = (int)$this->requestGet('limit', 20);

        if (!$query || strlen(trim($query)) < 2) {
            $this->renderSearchResults('products', $query, [], 0, $page, $limit, 'Търсеният израз трябва да бъде поне 2 символа');
            return;
        }

        $productController = $this->setBackendSubController('Common/GlobalSearch/Products', $this);
        if ($productController) {
            $data = $productController->searchAll($query, $page, $limit);
            $this->renderSearchResults('products', $query, $data['results'] ?? [], $data['total'] ?? 0, $page, $limit);
        } else {
            $this->renderSearchResults('products', $query, [], 0, $page, $limit, 'Грешка при зареждане на контролера за продукти');
        }
    }

    /**
     * Търсене във всички категории
     */
    public function categories() {
        $query = $this->requestGet('query');
        $page = (int)$this->requestGet('page', 1);
        $limit = (int)$this->requestGet('limit', 20);

        if (!$query || strlen(trim($query)) < 2) {
            $this->renderSearchResults('categories', $query, [], 0, $page, $limit, 'Търсеният израз трябва да бъде поне 2 символа');
            return;
        }

        $categoryController = $this->setBackendSubController('Common/GlobalSearch/Categories', $this);
        if ($categoryController) {
            $data = $categoryController->searchAll($query, $page, $limit);
            $this->renderSearchResults('categories', $query, $data['results'] ?? [], $data['total'] ?? 0, $page, $limit);
        } else {
            $this->renderSearchResults('categories', $query, [], 0, $page, $limit, 'Грешка при зареждане на контролера за категории');
        }
    }

    /**
     * Търсене във всички поръчки
     */
    public function orders() {
        $query = $this->requestGet('query');
        $page = (int)$this->requestGet('page', 1);
        $limit = (int)$this->requestGet('limit', 20);

        if (!$query || strlen(trim($query)) < 2) {
            $this->renderSearchResults('orders', $query, [], 0, $page, $limit, 'Търсеният израз трябва да бъде поне 2 символа');
            return;
        }

        $orderController = $this->setBackendSubController('Common/GlobalSearch/Orders', $this);
        if ($orderController) {
            $data = $orderController->searchAll($query, $page, $limit);
            $this->renderSearchResults('orders', $query, $data['results'] ?? [], $data['total'] ?? 0, $page, $limit);
        } else {
            $this->renderSearchResults('orders', $query, [], 0, $page, $limit, 'Грешка при зареждане на контролера за поръчки');
        }
    }

    /**
     * Търсене във всички клиенти
     */
    public function customers() {
        $query = $this->requestGet('query');
        $page = (int)$this->requestGet('page', 1);
        $limit = (int)$this->requestGet('limit', 20);

        if (!$query || strlen(trim($query)) < 2) {
            $this->renderSearchResults('customers', $query, [], 0, $page, $limit, 'Търсеният израз трябва да бъде поне 2 символа');
            return;
        }

        $customerController = $this->setBackendSubController('Common/GlobalSearch/Customers', $this);
        if ($customerController) {
            $data = $customerController->searchAll($query, $page, $limit);
            $this->renderSearchResults('customers', $query, $data['results'] ?? [], $data['total'] ?? 0, $page, $limit);
        } else {
            $this->renderSearchResults('customers', $query, [], 0, $page, $limit, 'Грешка при зареждане на контролера за клиенти');
        }
    }

    /**
     * Рендериране на резултатите от търсенето
     */
    private function renderSearchResults($type, $query, $results, $total, $page, $limit, $error = null) {
        $data = [
            'header' => $this->load->controller('common/header'),
            'sidebar' => $this->load->controller('common/sidebar'),
            'footer' => $this->load->controller('common/footer'),
            'user_token' => $this->session->data['user_token'],
            'type' => $type,
            'query' => $query,
            'results' => $results,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'error' => $error
        ];

        // Заглавия за различните типове
        $titles = [
            'products' => 'Продукти',
            'categories' => 'Категории',
            'orders' => 'Поръчки',
            'customers' => 'Клиенти'
        ];

        $data['title'] = $titles[$type] ?? 'Резултати';

        // Пагинация
        if ($total > $limit) {
            $totalPages = ceil($total / $limit);
            $pagination = '';

            // Предишна страница
            if ($page > 1) {
                $prevPage = $page - 1;
                $pagination .= '<a href="index.php?route=common/globalsearch/' . $type . '&query=' . urlencode($query) . '&page=' . $prevPage . '&user_token=' . $this->session->data['user_token'] . '" class="paginate_button previous">Предишна</a>';
            }

            // Номера на страници
            $start = max(1, $page - 2);
            $end = min($totalPages, $page + 2);

            for ($i = $start; $i <= $end; $i++) {
                $active = ($i == $page) ? ' current' : '';
                $pagination .= '<a href="index.php?route=common/globalsearch/' . $type . '&query=' . urlencode($query) . '&page=' . $i . '&user_token=' . $this->session->data['user_token'] . '" class="paginate_button' . $active . '">' . $i . '</a>';
            }

            // Следваща страница
            if ($page < $totalPages) {
                $nextPage = $page + 1;
                $pagination .= '<a href="index.php?route=common/globalsearch/' . $type . '&query=' . urlencode($query) . '&page=' . $nextPage . '&user_token=' . $this->session->data['user_token'] . '" class="paginate_button next">Следваща</a>';
            }

            $data['pagination'] = $pagination;
        }

        $this->response->setOutput($this->load->view('common/global_search_results', $data));
    }

     /**
     * Подготвя данните за езиците.
     */
    private function prepareLanguageData() {
        $this->loadModelsAs([
            'localisation/language'   => 'languageModel',
        ]);
        $active_language_id = $this->getConfig('config_language_id');
        $active_language_id_sdb = $this->getConfigFromSecondDB('config_language_id');

        if (!empty($active_language_id_sdb)) {
            $active_language_id = $active_language_id_sdb;
        }

        $languages_data = $this->languageModel->getLanguages();
        $languages = [];
        $language_exists = false;

        foreach ($languages_data as $language) {
            $languages[] = [
                'language_id' => $language['language_id'],
                'code' => $language['code'],
                'name' => $language['name'],
                'css' => $language['code'] == 'bg-bg' ? ' bg-languagebg' : ' bg-languageen'
            ];
            if ($language['language_id'] == $active_language_id) {
                $language_exists = true;
            }
        }

        $this->setData('languages', $languages);

        if (!$language_exists && !empty($languages)) {
            $active_language_id = $languages[0]['language_id'];
        }

        $this->setData('active_language_id', $active_language_id);
    }

    /**
     * Изчиства кеша за търсенето
     */
    public function clearCache() {
        $json = ['success' => false];

        try {
            // Изчистваме кеша за всички под-контролери
            $productController = $this->setBackendSubController('Common/GlobalSearch/Products', $this);
            if ($productController) {
                $productController->clearCache();
            }

            $categoryController = $this->setBackendSubController('Common/GlobalSearch/Categories', $this);
            if ($categoryController) {
                $categoryController->clearCache();
            }

            $orderController = $this->setBackendSubController('Common/GlobalSearch/Orders', $this);
            if ($orderController) {
                $orderController->clearCache();
            }

            $customerController = $this->setBackendSubController('Common/GlobalSearch/Customers', $this);
            if ($customerController) {
                $customerController->clearCache();
            }

            $json['success'] = true;
            $json['message'] = 'Кешът за търсенето е изчистен успешно';

        } catch (Exception $e) {
            $json['error'] = 'Грешка при изчистване на кеша: ' . $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    private function setCacheDir() {
        $this->cacheDir = DIR_CACHE . $this->cacheDir;
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }

}
